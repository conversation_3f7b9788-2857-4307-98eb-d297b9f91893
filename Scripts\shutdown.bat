@echo off
REM Graceful shutdown script for N8N_Builder system

echo ========================================
echo N8N_Builder Graceful Shutdown
echo ========================================
echo.

echo [INFO] Attempting graceful shutdown...
python Scripts\shutdown.py

if %errorlevel% neq 0 (
    echo.
    echo [WARNING] Graceful shutdown failed!
    echo [INFO] Would you like to try emergency shutdown? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo [INFO] Running emergency shutdown...
        call Scripts\emergency_shutdown.bat
    )
) else (
    echo.
    echo [SUCCESS] Graceful shutdown completed successfully!
)

echo.
pause
